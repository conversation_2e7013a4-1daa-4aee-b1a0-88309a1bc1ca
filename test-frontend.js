#!/usr/bin/env node

const axios = require('axios');

const BASE_URL = 'http://localhost:8081';
const FRONTEND_URL = 'http://localhost:4000';

// Test data
const demoCredentials = [
  { email: '<EMAIL>', password: 'admin123' },
  { email: '<EMAIL>', password: 'demo123' }
];

async function makeRequest(method, endpoint, data = null, token = null) {
  try {
    const config = {
      method,
      url: `${BASE_URL}${endpoint}`,
      headers: {
        'Content-Type': 'application/json',
        ...(token && { 'Authorization': `Bearer ${token}` })
      },
      ...(data && { data })
    };

    const response = await axios(config);
    return { success: true, data: response.data, status: response.status };
  } catch (error) {
    return { 
      success: false, 
      error: error.response?.data || error.message, 
      status: error.response?.status 
    };
  }
}

async function testDemoLogin() {
  console.log('🔐 Testing Demo Login Credentials...\n');
  
  for (const creds of demoCredentials) {
    console.log(`Testing login: ${creds.email}`);
    
    const result = await makeRequest('POST', '/auth/login', {
      login: creds.email,
      password: creds.password
    });
    
    if (result.success) {
      console.log(`✅ Login successful for ${creds.email}`);
      console.log(`   Token: ${result.data.data.token.substring(0, 20)}...`);
      
      // Test accessing protected route
      const userResult = await makeRequest('GET', '/api/settings/user', null, result.data.data.token);
      if (userResult.success) {
        console.log(`✅ Protected route access successful`);
        console.log(`   User: ${userResult.data.data.user.firstName} ${userResult.data.data.user.lastName}`);
      } else {
        console.log(`❌ Protected route access failed:`, userResult.error);
      }
    } else {
      console.log(`❌ Login failed for ${creds.email}:`, result.error);
    }
    console.log('');
  }
}

async function testRateLimiting() {
  console.log('⚡ Testing Rate Limiting...\n');
  
  const requests = [];
  const startTime = Date.now();
  
  // Make 10 rapid requests
  for (let i = 0; i < 10; i++) {
    requests.push(makeRequest('GET', '/health'));
  }
  
  const results = await Promise.all(requests);
  const endTime = Date.now();
  
  const successful = results.filter(r => r.success).length;
  const rateLimited = results.filter(r => r.status === 429).length;
  
  console.log(`Made 10 requests in ${endTime - startTime}ms`);
  console.log(`✅ Successful: ${successful}`);
  console.log(`⚠️  Rate limited: ${rateLimited}`);
  
  if (successful >= 8) {
    console.log('✅ Rate limiting is properly configured (not too restrictive)');
  } else {
    console.log('❌ Rate limiting might be too restrictive');
  }
  console.log('');
}

async function testPipelineWorkflow() {
  console.log('🔧 Testing Complete Pipeline Workflow...\n');
  
  // Login first
  const loginResult = await makeRequest('POST', '/auth/login', {
    login: '<EMAIL>',
    password: 'admin123'
  });
  
  if (!loginResult.success) {
    console.log('❌ Cannot test pipeline workflow - login failed');
    return;
  }
  
  const token = loginResult.data.data.token;
  console.log('✅ Logged in successfully');
  
  // Create project
  const projectData = {
    name: 'Frontend Test Project',
    slug: 'frontend-test-project',
    description: 'Test project for frontend validation',
    repositoryUrl: 'https://github.com/test/frontend-repo',
    defaultBranch: 'main'
  };
  
  const projectResult = await makeRequest('POST', '/api/projects', projectData, token);
  if (!projectResult.success) {
    console.log('❌ Project creation failed:', projectResult.error);
    return;
  }
  
  const projectId = projectResult.data.data.project.id;
  console.log(`✅ Project created: ${projectId}`);
  
  // Create pipeline
  const pipelineData = {
    name: 'Frontend Test Pipeline',
    slug: 'frontend-test-pipeline',
    description: 'Test pipeline for frontend validation',
    projectId,
    config: {
      name: 'Frontend Test CI/CD',
      on: {
        push: { branches: ['main'] }
      },
      jobs: {
        test: {
          'runs-on': 'ubuntu-latest',
          steps: [
            { name: 'Checkout', uses: 'actions/checkout@v3' },
            { name: 'Test', run: 'npm test' }
          ]
        }
      }
    }
  };
  
  const pipelineResult = await makeRequest('POST', '/api/pipelines', pipelineData, token);
  if (!pipelineResult.success) {
    console.log('❌ Pipeline creation failed:', pipelineResult.error);
    return;
  }
  
  const pipelineId = pipelineResult.data.data.pipeline.id;
  console.log(`✅ Pipeline created: ${pipelineId}`);
  
  // Create secret
  const secretData = {
    key: 'FRONTEND_TEST_SECRET',
    value: 'secret-value-123',
    description: 'Test secret for frontend validation',
    projectId
  };
  
  const secretResult = await makeRequest('POST', '/api/secrets', secretData, token);
  if (!secretResult.success) {
    console.log('❌ Secret creation failed:', secretResult.error);
    return;
  }
  
  console.log(`✅ Secret created: ${secretResult.data.data.secret.id}`);
  
  // Test pipeline execution
  const runResult = await makeRequest('POST', `/api/pipelines/${pipelineId}/run`, {
    variables: { NODE_ENV: 'test' },
    branch: 'main'
  }, token);
  
  if (!runResult.success) {
    console.log('❌ Pipeline execution failed:', runResult.error);
    return;
  }
  
  console.log(`✅ Pipeline executed: ${runResult.data.data.pipelineRun.id}`);
  
  // Test pipeline update (simulating edit page functionality)
  const updateResult = await makeRequest('PUT', `/api/pipelines/${pipelineId}`, {
    description: 'Updated description from frontend test'
  }, token);
  
  if (!updateResult.success) {
    console.log('❌ Pipeline update failed:', updateResult.error);
    return;
  }
  
  console.log(`✅ Pipeline updated successfully`);
  console.log('✅ Complete pipeline workflow test passed!');
  console.log('');
}

async function testFrontendRoutes() {
  console.log('🌐 Testing Frontend Route Accessibility...\n');
  
  const routes = [
    '/',
    '/auth/login',
    '/auth/register',
    '/dashboard',
    '/projects',
    '/pipelines',
    '/jobs',
    '/secrets',
    '/settings'
  ];
  
  for (const route of routes) {
    try {
      const response = await axios.get(`${FRONTEND_URL}${route}`, {
        timeout: 5000,
        validateStatus: (status) => status < 500 // Accept redirects and client errors
      });
      
      if (response.status < 400) {
        console.log(`✅ ${route} - Accessible (${response.status})`);
      } else {
        console.log(`⚠️  ${route} - Client error (${response.status})`);
      }
    } catch (error) {
      if (error.code === 'ECONNREFUSED') {
        console.log(`❌ ${route} - Frontend server not running`);
      } else {
        console.log(`❌ ${route} - Error: ${error.message}`);
      }
    }
  }
  console.log('');
}

async function runAllTests() {
  console.log('🧪 Starting ChainOps Frontend Integration Tests...\n');
  
  await testRateLimiting();
  await testDemoLogin();
  await testPipelineWorkflow();
  await testFrontendRoutes();
  
  console.log('📊 Frontend Integration Tests Complete!');
  console.log('✅ All critical functionality has been tested');
  console.log('');
  console.log('🎯 Next Steps:');
  console.log('1. Open http://localhost:4000 in your browser');
  console.log('2. <NAME_EMAIL> / admin123');
  console.log('3. Test the pipeline edit functionality at /pipelines/:id/edit');
  console.log('4. Verify job status filtering works correctly');
  console.log('5. Check that error handling displays properly');
}

// Run tests
runAllTests().catch(console.error);
