import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { pipelineService } from '@/services/pipelines';
import { toast } from 'react-hot-toast';

interface QuickAction {
  name: string;
  description: string;
  href?: string;
  icon: string;
  color: string;
  action?: () => void;
  requiresData?: boolean;
}

export function QuickActions() {
  const router = useRouter();
  const [showRunPipelineModal, setShowRunPipelineModal] = useState(false);

  // Fetch pipelines for the "Run Pipeline" action
  const { data: pipelines } = useQuery({
    queryKey: ['pipelines'],
    queryFn: () => pipelineService.getPipelines(),
    enabled: showRunPipelineModal,
  });

  const handleRunPipeline = () => {
    setShowRunPipelineModal(true);
  };

  const handlePipelineSelect = (pipelineId: string) => {
    setShowRunPipelineModal(false);
    router.push(`/pipelines/${pipelineId}/runs/new`);
  };

  const quickActions: QuickAction[] = [
    {
      name: 'Create Project',
      description: 'Set up a new CI/CD project',
      href: '/projects/new',
      icon: 'project',
      color: 'bg-blue-500 hover:bg-blue-600',
    },
    {
      name: 'New Pipeline',
      description: 'Create a new pipeline',
      href: '/pipelines/new',
      icon: 'pipeline',
      color: 'bg-green-500 hover:bg-green-600',
    },
    {
      name: 'Run Pipeline',
      description: 'Trigger a pipeline manually',
      icon: 'run',
      color: 'bg-purple-500 hover:bg-purple-600',
      action: handleRunPipeline,
      requiresData: true,
    },
    {
      name: 'View Logs',
      description: 'Check recent job logs',
      href: '/jobs',
      icon: 'logs',
      color: 'bg-orange-500 hover:bg-orange-600',
    },
    {
      name: 'Manage Secrets',
      description: 'Configure environment secrets',
      href: '/secrets',
      icon: 'secrets',
      color: 'bg-red-500 hover:bg-red-600',
    },
    {
      name: 'Settings',
      description: 'Configure your account',
      href: '/settings',
      icon: 'settings',
      color: 'bg-gray-500 hover:bg-gray-600',
    },
  ];

  const getIcon = (iconName: string) => {
    const baseClasses = "w-6 h-6 text-white";
    
    const icons = {
      project: (
        <svg className={baseClasses} fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
        </svg>
      ),
      pipeline: (
        <svg className={baseClasses} fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
        </svg>
      ),
      run: (
        <svg className={baseClasses} fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h1m4 0h1m-6-8h1m4 0h1M9 6h6" />
        </svg>
      ),
      logs: (
        <svg className={baseClasses} fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
      ),
      secrets: (
        <svg className={baseClasses} fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z" />
        </svg>
      ),
      settings: (
        <svg className={baseClasses} fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
        </svg>
      ),
    };
    
    return icons[iconName as keyof typeof icons] || icons.settings;
  };

  return (
    <>
      <div className="bg-white shadow rounded-lg">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">Quick Actions</h3>
        </div>
        <div className="p-6">
          <div className="grid grid-cols-1 gap-4">
            {quickActions.map((action) => {
              const ActionComponent = action.href ? Link : 'button';
              const actionProps = action.href
                ? { href: action.href }
                : { onClick: action.action, type: 'button' as const };

              return (
                <ActionComponent
                  key={action.name}
                  {...actionProps}
                  className="group relative rounded-lg p-4 border border-gray-200 hover:border-gray-300 hover:shadow-md transition-all duration-200 text-left w-full"
                >
                  <div className="flex items-start space-x-3">
                    <div className={`flex-shrink-0 w-10 h-10 rounded-lg ${action.color} flex items-center justify-center transition-colors duration-200`}>
                      {getIcon(action.icon)}
                    </div>
                    <div className="flex-1 min-w-0">
                      <h4 className="text-sm font-medium text-gray-900 group-hover:text-primary-600 transition-colors duration-200">
                        {action.name}
                      </h4>
                      <p className="text-sm text-gray-500 mt-1">
                        {action.description}
                      </p>
                    </div>
                    <div className="flex-shrink-0">
                      <svg className="w-5 h-5 text-gray-400 group-hover:text-primary-500 transition-colors duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                      </svg>
                    </div>
                  </div>
                </ActionComponent>
              );
            })}
          </div>

          <div className="mt-6 pt-6 border-t border-gray-200">
            <div className="text-center">
              <h4 className="text-sm font-medium text-gray-900 mb-2">Need Help?</h4>
              <div className="space-y-2">
                <Link
                  href="/docs"
                  className="block text-sm text-primary-600 hover:text-primary-500"
                >
                  📚 Documentation
                </Link>
                <Link
                  href="/support"
                  className="block text-sm text-primary-600 hover:text-primary-500"
                >
                  💬 Get Support
                </Link>
                <Link
                  href="/examples"
                  className="block text-sm text-primary-600 hover:text-primary-500"
                >
                  🚀 View Examples
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Run Pipeline Modal */}
      {showRunPipelineModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Select Pipeline to Run</h3>

              {pipelines?.data?.length > 0 ? (
                <div className="space-y-2 max-h-60 overflow-y-auto">
                  {pipelines.data.map((pipeline: any) => (
                    <button
                      key={pipeline.id}
                      onClick={() => handlePipelineSelect(pipeline.id)}
                      className="w-full text-left p-3 border border-gray-200 rounded-md hover:bg-gray-50 transition-colors"
                    >
                      <div className="font-medium text-gray-900">{pipeline.name}</div>
                      <div className="text-sm text-gray-500">{pipeline.description || 'No description'}</div>
                    </button>
                  ))}
                </div>
              ) : (
                <div className="text-center py-4">
                  <p className="text-gray-500 mb-4">No pipelines available</p>
                  <Link
                    href="/pipelines/new"
                    className="text-blue-600 hover:text-blue-800"
                    onClick={() => setShowRunPipelineModal(false)}
                  >
                    Create your first pipeline
                  </Link>
                </div>
              )}

              <div className="mt-4 flex justify-end space-x-3">
                <button
                  onClick={() => setShowRunPipelineModal(false)}
                  className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
