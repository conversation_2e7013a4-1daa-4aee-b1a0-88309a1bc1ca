import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { useRouter } from 'next/navigation';
import { toast } from 'react-hot-toast';
import { QuickActions } from '../QuickActions';
import { pipelineService } from '@/services/pipelines';

// Mock dependencies
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
}));

jest.mock('react-hot-toast', () => ({
  toast: {
    error: jest.fn(),
    success: jest.fn(),
  },
}));

jest.mock('@/services/pipelines', () => ({
  pipelineService: {
    getPipelines: jest.fn(),
  },
}));

const mockRouter = {
  push: jest.fn(),
  back: jest.fn(),
  forward: jest.fn(),
  refresh: jest.fn(),
  replace: jest.fn(),
  prefetch: jest.fn(),
};

const mockPipelines = [
  {
    id: 'pipeline-1',
    name: 'Frontend CI/CD',
    description: 'Frontend build and deployment pipeline',
    isActive: true,
  },
  {
    id: 'pipeline-2',
    name: 'Backend API',
    description: 'Backend API build and test pipeline',
    isActive: true,
  },
];

describe('QuickActions', () => {
  let queryClient: QueryClient;

  beforeEach(() => {
    queryClient = new QueryClient({
      defaultOptions: {
        queries: {
          retry: false,
        },
      },
    });
    
    (useRouter as jest.Mock).mockReturnValue(mockRouter);
    jest.clearAllMocks();
  });

  const renderWithProviders = (component: React.ReactElement) => {
    return render(
      <QueryClientProvider client={queryClient}>
        {component}
      </QueryClientProvider>
    );
  };

  it('renders all quick actions', () => {
    renderWithProviders(<QuickActions />);

    expect(screen.getByText('Quick Actions')).toBeInTheDocument();
    expect(screen.getByText('Create Project')).toBeInTheDocument();
    expect(screen.getByText('New Pipeline')).toBeInTheDocument();
    expect(screen.getByText('Run Pipeline')).toBeInTheDocument();
    expect(screen.getByText('View Logs')).toBeInTheDocument();
    expect(screen.getByText('Manage Secrets')).toBeInTheDocument();
    expect(screen.getByText('Settings')).toBeInTheDocument();
  });

  it('renders quick action descriptions', () => {
    renderWithProviders(<QuickActions />);

    expect(screen.getByText('Set up a new CI/CD project')).toBeInTheDocument();
    expect(screen.getByText('Create a new pipeline')).toBeInTheDocument();
    expect(screen.getByText('Trigger a pipeline manually')).toBeInTheDocument();
    expect(screen.getByText('Check recent job logs')).toBeInTheDocument();
    expect(screen.getByText('Configure environment secrets')).toBeInTheDocument();
    expect(screen.getByText('Configure your account')).toBeInTheDocument();
  });

  it('navigates to create project page when clicked', () => {
    renderWithProviders(<QuickActions />);

    const createProjectButton = screen.getByText('Create Project').closest('a');
    expect(createProjectButton).toHaveAttribute('href', '/projects/new');
  });

  it('navigates to new pipeline page when clicked', () => {
    renderWithProviders(<QuickActions />);

    const newPipelineButton = screen.getByText('New Pipeline').closest('a');
    expect(newPipelineButton).toHaveAttribute('href', '/pipelines/new');
  });

  it('navigates to jobs page when view logs is clicked', () => {
    renderWithProviders(<QuickActions />);

    const viewLogsButton = screen.getByText('View Logs').closest('a');
    expect(viewLogsButton).toHaveAttribute('href', '/jobs');
  });

  it('navigates to secrets page when manage secrets is clicked', () => {
    renderWithProviders(<QuickActions />);

    const manageSecretsButton = screen.getByText('Manage Secrets').closest('a');
    expect(manageSecretsButton).toHaveAttribute('href', '/secrets');
  });

  it('navigates to settings page when settings is clicked', () => {
    renderWithProviders(<QuickActions />);

    const settingsButton = screen.getByText('Settings').closest('a');
    expect(settingsButton).toHaveAttribute('href', '/settings');
  });

  it('opens run pipeline modal when run pipeline is clicked', async () => {
    (pipelineService.getPipelines as jest.Mock).mockResolvedValue({
      data: mockPipelines,
    });

    renderWithProviders(<QuickActions />);

    const runPipelineButton = screen.getByText('Run Pipeline').closest('button');
    fireEvent.click(runPipelineButton!);

    await waitFor(() => {
      expect(screen.getByText('Select Pipeline to Run')).toBeInTheDocument();
    });
  });

  it('displays pipelines in the run pipeline modal', async () => {
    (pipelineService.getPipelines as jest.Mock).mockResolvedValue({
      data: mockPipelines,
    });

    renderWithProviders(<QuickActions />);

    const runPipelineButton = screen.getByText('Run Pipeline').closest('button');
    fireEvent.click(runPipelineButton!);

    await waitFor(() => {
      expect(screen.getByText('Frontend CI/CD')).toBeInTheDocument();
      expect(screen.getByText('Backend API')).toBeInTheDocument();
      expect(screen.getByText('Frontend build and deployment pipeline')).toBeInTheDocument();
      expect(screen.getByText('Backend API build and test pipeline')).toBeInTheDocument();
    });
  });

  it('navigates to pipeline run page when pipeline is selected', async () => {
    (pipelineService.getPipelines as jest.Mock).mockResolvedValue({
      data: mockPipelines,
    });

    renderWithProviders(<QuickActions />);

    const runPipelineButton = screen.getByText('Run Pipeline').closest('button');
    fireEvent.click(runPipelineButton!);

    await waitFor(() => {
      expect(screen.getByText('Frontend CI/CD')).toBeInTheDocument();
    });

    const pipelineButton = screen.getByText('Frontend CI/CD').closest('button');
    fireEvent.click(pipelineButton!);

    expect(mockRouter.push).toHaveBeenCalledWith('/pipelines/pipeline-1/runs/new');
  });

  it('shows no pipelines message when no pipelines are available', async () => {
    (pipelineService.getPipelines as jest.Mock).mockResolvedValue({
      data: [],
    });

    renderWithProviders(<QuickActions />);

    const runPipelineButton = screen.getByText('Run Pipeline').closest('button');
    fireEvent.click(runPipelineButton!);

    await waitFor(() => {
      expect(screen.getByText('No pipelines available')).toBeInTheDocument();
      expect(screen.getByText('Create your first pipeline')).toBeInTheDocument();
    });
  });

  it('closes modal when cancel is clicked', async () => {
    (pipelineService.getPipelines as jest.Mock).mockResolvedValue({
      data: mockPipelines,
    });

    renderWithProviders(<QuickActions />);

    const runPipelineButton = screen.getByText('Run Pipeline').closest('button');
    fireEvent.click(runPipelineButton!);

    await waitFor(() => {
      expect(screen.getByText('Select Pipeline to Run')).toBeInTheDocument();
    });

    const cancelButton = screen.getByText('Cancel');
    fireEvent.click(cancelButton);

    await waitFor(() => {
      expect(screen.queryByText('Select Pipeline to Run')).not.toBeInTheDocument();
    });
  });

  it('renders help section', () => {
    renderWithProviders(<QuickActions />);

    expect(screen.getByText('Need Help?')).toBeInTheDocument();
    expect(screen.getByText('📚 Documentation')).toBeInTheDocument();
    expect(screen.getByText('💬 Get Support')).toBeInTheDocument();
    expect(screen.getByText('🚀 View Examples')).toBeInTheDocument();
  });

  it('has correct styling classes for quick action buttons', () => {
    renderWithProviders(<QuickActions />);

    const createProjectButton = screen.getByText('Create Project').closest('a');
    expect(createProjectButton).toHaveClass('group', 'relative', 'rounded-lg', 'p-4');
  });

  it('handles pipeline service error gracefully', async () => {
    (pipelineService.getPipelines as jest.Mock).mockRejectedValue(new Error('API Error'));

    renderWithProviders(<QuickActions />);

    const runPipelineButton = screen.getByText('Run Pipeline').closest('button');
    fireEvent.click(runPipelineButton!);

    // Should still show the modal even if API fails
    await waitFor(() => {
      expect(screen.getByText('Select Pipeline to Run')).toBeInTheDocument();
    });
  });
});
