import { api } from './api';
import {
  Job,
  JobListResponse,
  JobFilters,
  PaginationParams,
  LogEntry,
  LogStream,
  Artifact,
} from '@/types/pipelines';

export const jobService = {
  // Get all jobs with filtering and pagination
  async getJobs(
    filters: JobFilters = {},
    pagination: PaginationParams = { page: 1, limit: 10 }
  ): Promise<Job[]> {
    try {
      const params = new URLSearchParams();
      params.append('page', pagination.page.toString());
      params.append('limit', pagination.limit.toString());

      if (filters.search) params.append('search', filters.search);
      if (filters.pipelineId) params.append('pipelineId', filters.pipelineId);
      if (filters.status) {
        if (Array.isArray(filters.status)) {
          filters.status.forEach(status => params.append('status', status));
        } else {
          params.append('status', filters.status);
        }
      }
      if (filters.environmentId) params.append('environmentId', filters.environmentId);

      const response = await api.get(`/api/jobs?${params.toString()}`);

      if (response.data.success && response.data.data) {
        return response.data.data.jobs || response.data.data;
      }

      // Return mock data for now
      return [
        {
          id: '1',
          name: 'Build Frontend',
          pipelineRunId: 'run-1',
          userId: 'user-1',
          status: 'SUCCESS',
          startedAt: '2024-01-15T10:00:00Z',
          finishedAt: '2024-01-15T10:05:00Z',
          createdAt: '2024-01-15T10:00:00Z',
          updatedAt: '2024-01-15T10:05:00Z',
          config: {
            runs_on: 'ubuntu-latest',
            steps: [
              { name: 'Checkout', uses: 'actions/checkout@v3' },
              { name: 'Build', run: 'npm run build' }
            ]
          }
        },
        {
          id: '2',
          name: 'Test API',
          pipelineRunId: 'run-2',
          userId: 'user-1',
          status: 'RUNNING',
          startedAt: '2024-01-15T10:10:00Z',
          createdAt: '2024-01-15T10:10:00Z',
          updatedAt: '2024-01-15T10:10:00Z',
          config: {
            runs_on: 'ubuntu-latest',
            steps: [
              { name: 'Checkout', uses: 'actions/checkout@v3' },
              { name: 'Test', run: 'npm test' }
            ]
          }
        },
        {
          id: '3',
          name: 'Deploy Production',
          pipelineRunId: 'run-3',
          userId: 'user-1',
          status: 'FAILED',
          startedAt: '2024-01-15T09:30:00Z',
          finishedAt: '2024-01-15T09:35:00Z',
          createdAt: '2024-01-15T09:30:00Z',
          updatedAt: '2024-01-15T09:35:00Z',
          config: {
            runs_on: 'ubuntu-latest',
            steps: [
              { name: 'Checkout', uses: 'actions/checkout@v3' },
              { name: 'Deploy', run: 'kubectl apply -f deployment.yaml' }
            ]
          }
        }
      ];
    } catch (error) {
      console.error('Failed to fetch jobs:', error);
      return [];
    }
  },

  // Get job by ID
  async getJob(id: string): Promise<Job | null> {
    try {
      const response = await api.get<{ data: { job: Job } }>(`/api/jobs/${id}`);
      return response.data.data.job;
    } catch (error) {
      console.error('Failed to fetch job:', error);
      return null;
    }
  },

  // Get job logs
  async getJobLogs(
    jobId: string,
    options: {
      limit?: number;
      offset?: number;
      level?: string;
      search?: string;
    } = {}
  ): Promise<LogEntry[]> {
    try {
      const params = new URLSearchParams();
      if (options.limit) params.append('limit', options.limit.toString());
      if (options.offset) params.append('offset', options.offset.toString());
      if (options.level) params.append('level', options.level);
      if (options.search) params.append('search', options.search);

      const response = await api.get<{ data: { logs: LogEntry[] } }>(
        `/api/jobs/${jobId}/logs?${params.toString()}`
      );
      return response.data.data.logs;
    } catch (error) {
      console.error('Failed to fetch job logs:', error);
      return [];
    }
  },

  // Get job artifacts
  async getJobArtifacts(jobId: string): Promise<Artifact[]> {
    try {
      const response = await api.get<{ data: { artifacts: Artifact[] } }>(
        `/api/jobs/${jobId}/artifacts`
      );
      return response.data.data.artifacts;
    } catch (error) {
      console.error('Failed to fetch job artifacts:', error);
      return [];
    }
  },

  // Download artifact
  async downloadArtifact(artifactId: string): Promise<Blob | null> {
    try {
      const response = await api.get(`/api/artifacts/${artifactId}/download`, {
        responseType: 'blob',
      });
      return response.data;
    } catch (error) {
      console.error('Failed to download artifact:', error);
      return null;
    }
  },

  // Cancel job
  async cancelJob(jobId: string): Promise<boolean> {
    try {
      await api.post(`/api/jobs/${jobId}/cancel`);
      return true;
    } catch (error) {
      console.error('Failed to cancel job:', error);
      return false;
    }
  },

  // Retry job
  async retryJob(jobId: string): Promise<Job | null> {
    try {
      const response = await api.post<{ data: { job: Job } }>(`/api/jobs/${jobId}/retry`);
      return response.data.data.job;
    } catch (error) {
      console.error('Failed to retry job:', error);
      return null;
    }
  },

  // Get job step details
  async getJobSteps(jobId: string): Promise<any[]> {
    try {
      const response = await api.get<{ data: { steps: any[] } }>(`/api/jobs/${jobId}/steps`);
      return response.data.data.steps;
    } catch (error) {
      console.error('Failed to fetch job steps:', error);
      return [];
    }
  },

  // Get job environment variables
  async getJobEnvironment(jobId: string): Promise<Record<string, string>> {
    try {
      const response = await api.get<{ data: { environment: Record<string, string> } }>(
        `/api/jobs/${jobId}/environment`
      );
      return response.data.data.environment;
    } catch (error) {
      console.error('Failed to fetch job environment:', error);
      return {};
    }
  },

  // Get job metrics
  async getJobMetrics(jobId: string): Promise<{
    duration: number;
    cpuUsage: number;
    memoryUsage: number;
    diskUsage: number;
  }> {
    try {
      const response = await api.get<{
        data: {
          duration: number;
          cpuUsage: number;
          memoryUsage: number;
          diskUsage: number;
        };
      }>(`/api/jobs/${jobId}/metrics`);
      return response.data.data;
    } catch (error) {
      console.error('Failed to fetch job metrics:', error);
      return {
        duration: 0,
        cpuUsage: 0,
        memoryUsage: 0,
        diskUsage: 0,
      };
    }
  },

  // Format log entry for display
  formatLogEntry(entry: LogEntry): string {
    const timestamp = new Date(entry.timestamp).toLocaleTimeString();
    const level = entry.level.toUpperCase().padEnd(5);
    return `[${timestamp}] ${level} ${entry.message}`;
  },

  // Get log level color
  getLogLevelColor(level: string): string {
    switch (level.toLowerCase()) {
      case 'error':
        return 'text-red-600';
      case 'warn':
        return 'text-yellow-600';
      case 'info':
        return 'text-blue-600';
      case 'debug':
        return 'text-gray-600';
      default:
        return 'text-gray-800';
    }
  },

  // Parse job duration
  formatDuration(startedAt?: string, finishedAt?: string): string {
    if (!startedAt) return 'Not started';
    if (!finishedAt) return 'Running...';

    const start = new Date(startedAt);
    const end = new Date(finishedAt);
    const duration = end.getTime() - start.getTime();

    const seconds = Math.floor(duration / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);

    if (hours > 0) {
      return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`;
    } else {
      return `${seconds}s`;
    }
  },

  // Get status icon
  getStatusIcon(status: string): string {
    switch (status.toLowerCase()) {
      case 'success':
        return '✅';
      case 'failed':
        return '❌';
      case 'running':
        return '🔄';
      case 'pending':
      case 'queued':
        return '⏳';
      case 'cancelled':
        return '🚫';
      case 'skipped':
        return '⏭️';
      default:
        return '❓';
    }
  },

  // Get status color class
  getStatusColorClass(status: string): string {
    switch (status.toLowerCase()) {
      case 'success':
        return 'text-green-600 bg-green-100';
      case 'failed':
        return 'text-red-600 bg-red-100';
      case 'running':
        return 'text-blue-600 bg-blue-100';
      case 'pending':
      case 'queued':
        return 'text-yellow-600 bg-yellow-100';
      case 'cancelled':
        return 'text-gray-600 bg-gray-100';
      case 'skipped':
        return 'text-purple-600 bg-purple-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  },

  // Format file size
  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';

    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  },
};
