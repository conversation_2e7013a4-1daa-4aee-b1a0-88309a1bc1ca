'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'react-hot-toast';
import { ArrowLeftIcon } from '@heroicons/react/24/outline';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { pipelineService } from '@/services/pipelines';
import { projectService } from '@/services/project';

interface CreatePipelineForm {
  name: string;
  slug: string;
  description: string;
  projectId: string;
  config: string;
  isActive: boolean;
}

const defaultConfig = `name: CI/CD Pipeline
on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Run tests
        run: npm test
      
      - name: Build application
        run: npm run build

  deploy:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
      
      - name: Deploy to staging
        run: echo "Deploying to staging environment"`;

export default function NewPipelinePage() {
  const router = useRouter();
  const queryClient = useQueryClient();
  
  const [formData, setFormData] = useState<CreatePipelineForm>({
    name: '',
    slug: '',
    description: '',
    projectId: '',
    config: defaultConfig,
    isActive: true,
  });

  // Fetch projects for selection
  const { data: projects, isLoading: projectsLoading } = useQuery({
    queryKey: ['projects'],
    queryFn: () => projectService.getProjects(),
  });

  const createPipelineMutation = useMutation({
    mutationFn: pipelineService.createPipeline,
    onSuccess: (pipeline) => {
      toast.success('Pipeline created successfully!');
      queryClient.invalidateQueries({ queryKey: ['pipelines'] });
      if (pipeline) {
        router.push(`/pipelines/${pipeline.id}`);
      }
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || 'Failed to create pipeline');
    },
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name.trim()) {
      toast.error('Pipeline name is required');
      return;
    }

    if (!formData.projectId) {
      toast.error('Please select a project');
      return;
    }

    if (!formData.config.trim()) {
      toast.error('Pipeline configuration is required');
      return;
    }

    // Auto-generate slug if not provided
    const slug = formData.slug || formData.name.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/^-|-$/g, '');
    
    try {
      // Parse config to validate it
      let parsedConfig;
      try {
        parsedConfig = JSON.parse(formData.config);
      } catch {
        // If JSON parsing fails, treat as YAML string
        parsedConfig = formData.config;
      }

      await createPipelineMutation.mutateAsync({
        ...formData,
        slug,
        name: formData.name.trim(),
        description: formData.description.trim(),
        config: parsedConfig,
      });
    } catch (error) {
      console.error('Failed to create pipeline:', error);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    const checked = (e.target as HTMLInputElement).checked;
    
    setFormData(prev => ({ 
      ...prev, 
      [name]: type === 'checkbox' ? checked : value 
    }));
    
    // Auto-generate slug when name changes
    if (name === 'name' && !formData.slug) {
      const autoSlug = value.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/^-|-$/g, '');
      setFormData(prev => ({ ...prev, slug: autoSlug }));
    }
  };

  const handleConfigChange = (value: string) => {
    setFormData(prev => ({ ...prev, config: value }));
  };

  if (projectsLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <LoadingSpinner size="lg" />
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="max-w-4xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <button
            onClick={() => router.back()}
            className="flex items-center text-gray-600 hover:text-gray-900 mb-4 transition-colors"
          >
            <ArrowLeftIcon className="w-5 h-5 mr-2" />
            Back
          </button>
          <h1 className="text-3xl font-bold text-gray-900">Create New Pipeline</h1>
          <p className="text-gray-600 mt-2">
            Set up a new CI/CD pipeline to automate your build, test, and deployment processes.
          </p>
        </div>

        {/* Form */}
        <div className="bg-white shadow rounded-lg">
          <form onSubmit={handleSubmit} className="p-6 space-y-6">
            {/* Basic Information */}
            <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
              <div>
                <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                  Pipeline Name *
                </label>
                <input
                  type="text"
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleChange}
                  className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Enter pipeline name"
                  required
                />
              </div>

              <div>
                <label htmlFor="slug" className="block text-sm font-medium text-gray-700">
                  Pipeline Slug *
                </label>
                <input
                  type="text"
                  id="slug"
                  name="slug"
                  value={formData.slug}
                  onChange={handleChange}
                  className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                  placeholder="pipeline-slug"
                  pattern="^[a-z0-9-]+$"
                  title="Only lowercase letters, numbers, and hyphens allowed"
                  required
                />
              </div>
            </div>

            {/* Project Selection */}
            <div>
              <label htmlFor="projectId" className="block text-sm font-medium text-gray-700">
                Project *
              </label>
              <select
                id="projectId"
                name="projectId"
                value={formData.projectId}
                onChange={handleChange}
                className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                required
              >
                <option value="">Select a project</option>
                {projects?.map((project: any) => (
                  <option key={project.id} value={project.id}>
                    {project.name}
                  </option>
                ))}
              </select>
            </div>

            {/* Description */}
            <div>
              <label htmlFor="description" className="block text-sm font-medium text-gray-700">
                Description
              </label>
              <textarea
                id="description"
                name="description"
                rows={3}
                value={formData.description}
                onChange={handleChange}
                className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                placeholder="Describe your pipeline..."
              />
            </div>

            {/* Configuration */}
            <div>
              <label htmlFor="config" className="block text-sm font-medium text-gray-700">
                Pipeline Configuration *
              </label>
              <p className="mt-1 text-sm text-gray-500">
                Enter your pipeline configuration in YAML or JSON format
              </p>
              <textarea
                id="config"
                name="config"
                rows={20}
                value={formData.config}
                onChange={(e) => handleConfigChange(e.target.value)}
                className="mt-2 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 font-mono text-sm"
                placeholder="Enter pipeline configuration..."
                required
              />
            </div>

            {/* Status */}
            <div className="flex items-center">
              <input
                id="isActive"
                name="isActive"
                type="checkbox"
                checked={formData.isActive}
                onChange={handleChange}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label htmlFor="isActive" className="ml-2 block text-sm text-gray-900">
                Active (pipeline will be enabled after creation)
              </label>
            </div>

            {/* Submit Button */}
            <div className="flex justify-end space-x-3">
              <button
                type="button"
                onClick={() => router.back()}
                className="bg-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-400 transition-colors"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={createPipelineMutation.isPending}
                className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                {createPipelineMutation.isPending ? 'Creating...' : 'Create Pipeline'}
              </button>
            </div>
          </form>
        </div>

        {/* Info Box */}
        <div className="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h3 className="text-sm font-medium text-blue-800 mb-2">Pipeline Configuration Tips</h3>
          <ul className="text-sm text-blue-700 space-y-1">
            <li>• Use YAML or JSON format for your pipeline configuration</li>
            <li>• Define jobs with steps for build, test, and deployment</li>
            <li>• Set up triggers for push, pull request, or manual execution</li>
            <li>• Configure environment variables and secrets as needed</li>
          </ul>
        </div>
      </div>
    </DashboardLayout>
  );
}
