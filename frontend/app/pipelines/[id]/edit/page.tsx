'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'react-hot-toast';
import { pipelineService } from '@/services/pipelines';
import { projectService } from '@/services/projects';
import { Pipeline, Project } from '@/types';

export default function PipelineEditPage() {
  const params = useParams();
  const router = useRouter();
  const queryClient = useQueryClient();
  const pipelineId = params?.id as string;

  const [formData, setFormData] = useState({
    name: '',
    description: '',
    config: '',
    isActive: true,
  });

  // Fetch pipeline details
  const { 
    data: pipeline, 
    isLoading: pipelineLoading, 
    error: pipelineError 
  } = useQuery({
    queryKey: ['pipeline', pipelineId],
    queryFn: () => pipelineService.getPipeline(pipelineId),
    enabled: !!pipelineId,
  });

  // Fetch projects for project selection
  const { data: projects } = useQuery({
    queryKey: ['projects'],
    queryFn: () => projectService.getProjects(),
  });

  // Update form data when pipeline is loaded
  useEffect(() => {
    if (pipeline) {
      setFormData({
        name: pipeline.name || '',
        description: pipeline.description || '',
        config: typeof pipeline.config === 'string' 
          ? pipeline.config 
          : JSON.stringify(pipeline.config, null, 2),
        isActive: pipeline.isActive ?? true,
      });
    }
  }, [pipeline]);

  // Update pipeline mutation
  const updatePipelineMutation = useMutation({
    mutationFn: (data: any) => pipelineService.updatePipeline(pipelineId, data),
    onSuccess: () => {
      toast.success('Pipeline updated successfully!');
      queryClient.invalidateQueries({ queryKey: ['pipeline', pipelineId] });
      router.push(`/pipelines/${pipelineId}`);
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || 'Failed to update pipeline');
    },
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name.trim()) {
      toast.error('Pipeline name is required');
      return;
    }

    try {
      // Parse config to validate it
      let parsedConfig;
      try {
        parsedConfig = JSON.parse(formData.config);
      } catch {
        // If JSON parsing fails, treat as YAML string
        parsedConfig = formData.config;
      }

      await updatePipelineMutation.mutateAsync({
        name: formData.name.trim(),
        description: formData.description.trim(),
        config: parsedConfig,
        isActive: formData.isActive,
      });
    } catch (error) {
      console.error('Failed to update pipeline:', error);
    }
  };

  const handleConfigChange = (value: string) => {
    setFormData(prev => ({ ...prev, config: value }));
  };

  if (pipelineLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-2 border-gray-300 border-t-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading pipeline...</p>
        </div>
      </div>
    );
  }

  if (pipelineError) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Pipeline Not Found</h1>
          <p className="text-gray-600 mb-6">The pipeline you're looking for doesn't exist or you don't have permission to edit it.</p>
          <button
            onClick={() => router.push('/pipelines')}
            className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
          >
            Back to Pipelines
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Edit Pipeline</h1>
              <p className="mt-2 text-gray-600">
                Update your pipeline configuration and settings
              </p>
            </div>
            <button
              onClick={() => router.push(`/pipelines/${pipelineId}`)}
              className="bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700"
            >
              Cancel
            </button>
          </div>
        </div>

        {/* Form */}
        <div className="bg-white shadow rounded-lg">
          <form onSubmit={handleSubmit} className="p-6 space-y-6">
            {/* Basic Information */}
            <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
              <div>
                <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                  Pipeline Name *
                </label>
                <input
                  type="text"
                  id="name"
                  value={formData.name}
                  onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                  className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Enter pipeline name"
                  required
                />
              </div>

              <div>
                <label htmlFor="isActive" className="block text-sm font-medium text-gray-700">
                  Status
                </label>
                <select
                  id="isActive"
                  value={formData.isActive ? 'active' : 'inactive'}
                  onChange={(e) => setFormData(prev => ({ ...prev, isActive: e.target.value === 'active' }))}
                  className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="active">Active</option>
                  <option value="inactive">Inactive</option>
                </select>
              </div>
            </div>

            {/* Description */}
            <div>
              <label htmlFor="description" className="block text-sm font-medium text-gray-700">
                Description
              </label>
              <textarea
                id="description"
                rows={3}
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                placeholder="Enter pipeline description"
              />
            </div>

            {/* Configuration */}
            <div>
              <label htmlFor="config" className="block text-sm font-medium text-gray-700">
                Pipeline Configuration *
              </label>
              <p className="mt-1 text-sm text-gray-500">
                Enter your pipeline configuration in YAML or JSON format
              </p>
              <textarea
                id="config"
                rows={20}
                value={formData.config}
                onChange={(e) => handleConfigChange(e.target.value)}
                className="mt-2 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 font-mono text-sm"
                placeholder="Enter pipeline configuration..."
                required
              />
            </div>

            {/* Submit Button */}
            <div className="flex justify-end space-x-3">
              <button
                type="button"
                onClick={() => router.push(`/pipelines/${pipelineId}`)}
                className="bg-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-400"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={updatePipelineMutation.isPending}
                className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {updatePipelineMutation.isPending ? 'Updating...' : 'Update Pipeline'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
