'use client';

import { useState } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'react-hot-toast';
import { ArrowLeftIcon, PlayIcon } from '@heroicons/react/24/outline';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { pipelineService } from '@/services/pipelines';

interface RunPipelineForm {
  branch: string;
  variables: Record<string, string>;
  description: string;
}

export default function NewPipelineRunPage() {
  const router = useRouter();
  const params = useParams();
  const queryClient = useQueryClient();
  const pipelineId = params.id as string;
  
  const [formData, setFormData] = useState<RunPipelineForm>({
    branch: 'main',
    variables: {},
    description: '',
  });

  const [variableInputs, setVariableInputs] = useState([{ key: '', value: '' }]);

  // Fetch pipeline details
  const { data: pipeline, isLoading } = useQuery({
    queryKey: ['pipeline', pipelineId],
    queryFn: () => pipelineService.getPipeline(pipelineId),
    enabled: !!pipelineId,
  });

  const runPipelineMutation = useMutation({
    mutationFn: (data: any) => pipelineService.runPipeline(pipelineId, data),
    onSuccess: (response) => {
      toast.success('Pipeline run started successfully!');
      queryClient.invalidateQueries({ queryKey: ['pipeline-runs'] });
      router.push(`/pipelines/${pipelineId}/runs/${response.data.pipelineRun.id}`);
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || 'Failed to start pipeline run');
    },
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.branch.trim()) {
      toast.error('Branch is required');
      return;
    }

    // Convert variable inputs to object
    const variables: Record<string, string> = {};
    variableInputs.forEach(({ key, value }) => {
      if (key.trim() && value.trim()) {
        variables[key.trim()] = value.trim();
      }
    });

    await runPipelineMutation.mutateAsync({
      branch: formData.branch.trim(),
      variables,
      description: formData.description.trim(),
    });
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleVariableChange = (index: number, field: 'key' | 'value', value: string) => {
    const newInputs = [...variableInputs];
    newInputs[index][field] = value;
    setVariableInputs(newInputs);
  };

  const addVariableInput = () => {
    setVariableInputs([...variableInputs, { key: '', value: '' }]);
  };

  const removeVariableInput = (index: number) => {
    if (variableInputs.length > 1) {
      setVariableInputs(variableInputs.filter((_, i) => i !== index));
    }
  };

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <LoadingSpinner size="lg" />
        </div>
      </DashboardLayout>
    );
  }

  if (!pipeline) {
    return (
      <DashboardLayout>
        <div className="text-center py-12">
          <h3 className="text-lg font-medium text-gray-900 mb-2">Pipeline not found</h3>
          <p className="text-gray-600 mb-6">The pipeline you're looking for doesn't exist.</p>
          <button
            onClick={() => router.push('/pipelines')}
            className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
          >
            Back to Pipelines
          </button>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="max-w-4xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <button
            onClick={() => router.back()}
            className="flex items-center text-gray-600 hover:text-gray-900 mb-4 transition-colors"
          >
            <ArrowLeftIcon className="w-5 h-5 mr-2" />
            Back
          </button>
          <div className="flex items-center space-x-3">
            <PlayIcon className="w-8 h-8 text-green-600" />
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Run Pipeline</h1>
              <p className="text-gray-600 mt-1">
                {pipeline.name} • {pipeline.project?.name}
              </p>
            </div>
          </div>
        </div>

        {/* Form */}
        <div className="bg-white shadow rounded-lg">
          <form onSubmit={handleSubmit} className="p-6 space-y-6">
            {/* Pipeline Info */}
            <div className="bg-gray-50 rounded-lg p-4">
              <h3 className="text-sm font-medium text-gray-900 mb-2">Pipeline Information</h3>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-gray-500">Name:</span>
                  <span className="ml-2 text-gray-900">{pipeline.name}</span>
                </div>
                <div>
                  <span className="text-gray-500">Status:</span>
                  <span className={`ml-2 px-2 py-1 rounded-full text-xs ${
                    pipeline.isActive
                      ? 'bg-green-100 text-green-800'
                      : 'bg-gray-100 text-gray-800'
                  }`}>
                    {pipeline.isActive ? 'Active' : 'Inactive'}
                  </span>
                </div>
              </div>
            </div>

            {/* Branch Selection */}
            <div>
              <label htmlFor="branch" className="block text-sm font-medium text-gray-700">
                Branch *
              </label>
              <input
                type="text"
                id="branch"
                name="branch"
                value={formData.branch}
                onChange={handleChange}
                className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                placeholder="main"
                required
              />
              <p className="mt-1 text-sm text-gray-500">
                The git branch to run the pipeline against
              </p>
            </div>

            {/* Environment Variables */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Environment Variables
              </label>
              <div className="space-y-3">
                {variableInputs.map((input, index) => (
                  <div key={index} className="flex space-x-3">
                    <input
                      type="text"
                      placeholder="Variable name"
                      value={input.key}
                      onChange={(e) => handleVariableChange(index, 'key', e.target.value)}
                      className="flex-1 border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                    />
                    <input
                      type="text"
                      placeholder="Variable value"
                      value={input.value}
                      onChange={(e) => handleVariableChange(index, 'value', e.target.value)}
                      className="flex-1 border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                    />
                    <button
                      type="button"
                      onClick={() => removeVariableInput(index)}
                      className="px-3 py-2 text-red-600 hover:text-red-800 transition-colors"
                      disabled={variableInputs.length === 1}
                    >
                      Remove
                    </button>
                  </div>
                ))}
              </div>
              <button
                type="button"
                onClick={addVariableInput}
                className="mt-2 text-sm text-blue-600 hover:text-blue-800 transition-colors"
              >
                + Add Variable
              </button>
            </div>

            {/* Description */}
            <div>
              <label htmlFor="description" className="block text-sm font-medium text-gray-700">
                Run Description
              </label>
              <textarea
                id="description"
                name="description"
                rows={3}
                value={formData.description}
                onChange={handleChange}
                className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                placeholder="Optional description for this pipeline run..."
              />
            </div>

            {/* Submit Button */}
            <div className="flex justify-end space-x-3">
              <button
                type="button"
                onClick={() => router.back()}
                className="bg-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-400 transition-colors"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={runPipelineMutation.isPending}
                className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center space-x-2"
              >
                <PlayIcon className="w-4 h-4" />
                <span>{runPipelineMutation.isPending ? 'Starting...' : 'Run Pipeline'}</span>
              </button>
            </div>
          </form>
        </div>

        {/* Info Box */}
        <div className="mt-6 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <h3 className="text-sm font-medium text-yellow-800 mb-2">Before Running</h3>
          <ul className="text-sm text-yellow-700 space-y-1">
            <li>• Ensure the specified branch exists in your repository</li>
            <li>• Environment variables will override any defaults in the pipeline</li>
            <li>• You can monitor the run progress in the pipeline runs section</li>
            <li>• Logs will be available in real-time once the run starts</li>
          </ul>
        </div>
      </div>
    </DashboardLayout>
  );
}
