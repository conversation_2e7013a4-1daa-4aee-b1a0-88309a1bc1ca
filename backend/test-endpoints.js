const axios = require('axios');

const BASE_URL = 'http://localhost:8081';
let authToken = '';
let userId = '';
let projectId = '';
let pipelineId = '';
let secretId = '';

// Generate unique test data
const timestamp = Date.now();
const testUser = {
  email: `test${timestamp}@chainops.dev`,
  password: 'testpassword123',
  username: `testuser${timestamp}`,
  firstName: 'Test',
  lastName: 'User'
};

const testProject = {
  name: `Test Project ${timestamp}`,
  slug: `test-project-${timestamp}`,
  description: 'A test project for CI/CD',
  repositoryUrl: 'https://github.com/test/repo',
  defaultBranch: 'main'
};

const testPipeline = {
  name: `Test Pipeline ${timestamp}`,
  slug: `test-pipeline-${timestamp}`,
  description: 'A test pipeline',
  config: {
    name: 'Test CI/CD',
    on: { push: { branches: ['main'] } },
    jobs: {
      build: {
        'runs-on': 'ubuntu-latest',
        steps: [
          { name: 'Checkout', uses: 'actions/checkout@v3' },
          { name: 'Build', run: 'npm install && npm run build' }
        ]
      }
    }
  }
};

const testSecret = {
  key: 'API_KEY',
  value: 'secret-api-key-value',
  description: 'Test API key'
};

async function makeRequest(method, endpoint, data = null, headers = {}) {
  try {
    const config = {
      method,
      url: `${BASE_URL}${endpoint}`,
      headers: {
        'Content-Type': 'application/json',
        ...headers
      }
    };
    
    if (authToken) {
      config.headers.Authorization = `Bearer ${authToken}`;
    }
    
    if (data) {
      config.data = data;
    }
    
    const response = await axios(config);
    return { success: true, data: response.data, status: response.status };
  } catch (error) {
    return { 
      success: false, 
      error: error.response?.data || error.message,
      status: error.response?.status 
    };
  }
}

async function testHealthCheck() {
  console.log('\n🔍 Testing Health Check...');
  const result = await makeRequest('GET', '/health');
  console.log('Health Check:', result.success ? '✅ PASS' : '❌ FAIL', result);
  return result.success;
}

async function testAuth() {
  console.log('\n🔐 Testing Authentication...');
  
  // Register user
  console.log('Registering user...');
  const registerResult = await makeRequest('POST', '/auth/register', testUser);
  console.log('Register:', registerResult.success ? '✅ PASS' : '❌ FAIL');

  // Login user
  console.log('Logging in user...');
  const loginResult = await makeRequest('POST', '/auth/login', {
    login: testUser.email,
    password: testUser.password
  });
  
  if (loginResult.success) {
    authToken = loginResult.data.data.token;
    userId = loginResult.data.data.user.id;
    console.log('Login: ✅ PASS');
    console.log('Auth token obtained:', authToken.substring(0, 20) + '...');
  } else {
    console.log('Login: ❌ FAIL', loginResult);
  }
  
  return loginResult.success;
}

async function testProjectCreation() {
  console.log('\n📁 Testing Project Creation...');
  
  const result = await makeRequest('POST', '/api/projects', testProject);
  
  if (result.success) {
    projectId = result.data.data.project.id;
    console.log('Project Creation: ✅ PASS');
    console.log('Project ID:', projectId);
  } else {
    console.log('Project Creation: ❌ FAIL', result);
  }
  
  return result.success;
}

async function testPipelineCreation() {
  console.log('\n🔧 Testing Pipeline Creation...');
  
  const pipelineData = {
    ...testPipeline,
    projectId
  };
  
  const result = await makeRequest('POST', '/api/pipelines', pipelineData);
  
  if (result.success) {
    pipelineId = result.data.data.pipeline.id;
    console.log('Pipeline Creation: ✅ PASS');
    console.log('Pipeline ID:', pipelineId);
  } else {
    console.log('Pipeline Creation: ❌ FAIL', result);
  }
  
  return result.success;
}

async function testSecretsManagement() {
  console.log('\n🔐 Testing Secrets Management...');
  
  // Create secret
  console.log('Creating secret...');
  const secretData = {
    ...testSecret,
    projectId
  };
  
  const createResult = await makeRequest('POST', '/api/secrets', secretData);
  
  if (createResult.success) {
    secretId = createResult.data.data.secret.id;
    console.log('Secret Creation: ✅ PASS');
    console.log('Secret ID:', secretId);
  } else {
    console.log('Secret Creation: ❌ FAIL', createResult);
    return false;
  }
  
  // Get secrets
  console.log('Getting secrets...');
  const getResult = await makeRequest('GET', `/api/secrets?projectId=${projectId}`);
  console.log('Get Secrets:', getResult.success ? '✅ PASS' : '❌ FAIL');
  
  // Get secret by ID
  console.log('Getting secret by ID...');
  const getByIdResult = await makeRequest('GET', `/api/secrets/${secretId}`);
  console.log('Get Secret by ID:', getByIdResult.success ? '✅ PASS' : '❌ FAIL');
  
  // Update secret
  console.log('Updating secret...');
  const updateResult = await makeRequest('PUT', `/api/secrets/${secretId}`, {
    description: 'Updated test API key'
  });
  console.log('Update Secret:', updateResult.success ? '✅ PASS' : '❌ FAIL');
  
  return createResult.success && getResult.success && getByIdResult.success && updateResult.success;
}

async function testSettingsManagement() {
  console.log('\n⚙️ Testing Settings Management...');
  
  // Get user settings
  console.log('Getting user settings...');
  const getUserResult = await makeRequest('GET', '/api/settings/user');
  console.log('Get User Settings:', getUserResult.success ? '✅ PASS' : '❌ FAIL');
  
  // Update user profile
  console.log('Updating user profile...');
  const updateProfileResult = await makeRequest('PUT', '/api/settings/user/profile', {
    firstName: 'Updated',
    lastName: 'Name'
  });
  console.log('Update Profile:', updateProfileResult.success ? '✅ PASS' : '❌ FAIL');
  
  // Update preferences
  console.log('Updating preferences...');
  const updatePrefsResult = await makeRequest('PUT', '/api/settings/user/preferences', {
    theme: 'dark',
    timezone: 'America/New_York'
  });
  console.log('Update Preferences:', updatePrefsResult.success ? '✅ PASS' : '❌ FAIL');
  
  // Get project settings
  console.log('Getting project settings...');
  const getProjectResult = await makeRequest('GET', `/api/settings/project/${projectId}`);
  console.log('Get Project Settings:', getProjectResult.success ? '✅ PASS' : '❌ FAIL');
  
  return getUserResult.success && updateProfileResult.success && 
         updatePrefsResult.success && getProjectResult.success;
}

async function testPipelineExecution() {
  console.log('\n🚀 Testing Pipeline Execution...');

  if (!pipelineId) {
    console.log('Pipeline Execution: ❌ FAIL - No pipeline ID available');
    return false;
  }

  const result = await makeRequest('POST', `/api/pipelines/${pipelineId}/run`, {
    variables: { NODE_ENV: 'test' },
    branch: 'main'
  });

  if (result.success) {
    console.log('Pipeline Execution: ✅ PASS');
    console.log('Pipeline Run ID:', result.data.data.pipelineRun.id);
  } else {
    console.log('Pipeline Execution: ❌ FAIL', result);
  }

  return result.success;
}

async function runAllTests() {
  console.log('🧪 Starting ChainOps Backend API Tests...\n');
  
  const tests = [
    { name: 'Health Check', fn: testHealthCheck },
    { name: 'Authentication', fn: testAuth },
    { name: 'Project Creation', fn: testProjectCreation },
    { name: 'Pipeline Creation', fn: testPipelineCreation },
    { name: 'Secrets Management', fn: testSecretsManagement },
    { name: 'Settings Management', fn: testSettingsManagement },
    { name: 'Pipeline Execution', fn: testPipelineExecution }
  ];
  
  const results = [];
  
  for (const test of tests) {
    try {
      const result = await test.fn();
      results.push({ name: test.name, passed: result });
    } catch (error) {
      console.log(`${test.name}: ❌ ERROR`, error.message);
      results.push({ name: test.name, passed: false, error: error.message });
    }
  }
  
  // Summary
  console.log('\n📊 Test Results Summary:');
  console.log('========================');
  
  const passed = results.filter(r => r.passed).length;
  const total = results.length;
  
  results.forEach(result => {
    const status = result.passed ? '✅ PASS' : '❌ FAIL';
    console.log(`${result.name}: ${status}`);
    if (result.error) {
      console.log(`  Error: ${result.error}`);
    }
  });
  
  console.log(`\nOverall: ${passed}/${total} tests passed`);
  
  if (passed === total) {
    console.log('🎉 All tests passed! Backend is working correctly.');
  } else {
    console.log('⚠️ Some tests failed. Please check the implementation.');
  }
}

// Run tests
runAllTests().catch(console.error);
