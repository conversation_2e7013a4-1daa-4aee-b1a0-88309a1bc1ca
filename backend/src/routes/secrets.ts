import { Router, Response } from 'express';
import { body, validationResult } from 'express-validator';
import { prisma } from '../services/database';
import { asyncHandler, CustomError } from '../middleware/errorHandler';
import { AuthenticatedRequest } from '../middleware/auth';
import { SecretsManager } from '../secrets/secrets';
import { logger } from '../utils/logger';
import crypto from 'crypto';

const router = Router();

// Initialize Vault secrets manager
const getSecretsManager = (): SecretsManager | null => {
  const vaultAddr = process.env.VAULT_ADDR;
  const vaultToken = process.env.VAULT_TOKEN;
  
  if (!vaultAddr || !vaultToken) {
    logger.warn('Vault configuration missing, using database-only secrets storage');
    return null;
  }
  
  return new SecretsManager(vaultAddr, vaultToken);
};

// Encrypt secret value for database storage
const encryptSecret = (value: string): string => {
  const algorithm = 'aes-256-cbc';
  const key = crypto.scryptSync(process.env.SECRET_ENCRYPTION_KEY || 'default-key', 'salt', 32);
  const iv = crypto.randomBytes(16);
  const cipher = crypto.createCipher(algorithm, key);

  let encrypted = cipher.update(value, 'utf8', 'hex');
  encrypted += cipher.final('hex');

  return `${iv.toString('hex')}:${encrypted}`;
};

// Decrypt secret value from database
const decryptSecret = (encryptedValue: string): string => {
  try {
    const algorithm = 'aes-256-cbc';
    const key = crypto.scryptSync(process.env.SECRET_ENCRYPTION_KEY || 'default-key', 'salt', 32);

    const parts = encryptedValue.split(':');
    if (parts.length !== 2) {
      // Fallback for plain text or different format
      return encryptedValue;
    }

    const [ivHex, encrypted] = parts;
    const decipher = crypto.createDecipher(algorithm, key);

    let decrypted = decipher.update(encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');

    return decrypted;
  } catch (error) {
    // If decryption fails, return the original value (might be plain text)
    return encryptedValue;
  }
};

// Get all secrets for user's projects
router.get('/', asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const { projectId, environment, search, page = 1, limit = 50 } = req.query;
  
  const skip = (Number(page) - 1) * Number(limit);
  const take = Number(limit);

  const where: any = {
    project: {
      members: {
        some: {
          userId: req.user!.id,
        },
      },
    },
  };
  
  if (projectId) {
    where.projectId = projectId as string;
  }
  
  if (search) {
    where.OR = [
      { key: { contains: search as string, mode: 'insensitive' } },
      { description: { contains: search as string, mode: 'insensitive' } },
    ];
  }

  const [secrets, total] = await Promise.all([
    prisma.secret.findMany({
      where,
      skip,
      take,
      include: {
        project: {
          select: {
            id: true,
            name: true,
            slug: true,
          },
        },
      },
      orderBy: { updatedAt: 'desc' },
    }),
    prisma.secret.count({ where }),
  ]);

  // Decrypt values for response (mask them for security)
  const secretsWithMaskedValues = secrets.map(secret => ({
    ...secret,
    value: '*'.repeat(Math.min(secret.value.length, 20)),
    maskedValue: true,
  }));

  res.json({
    success: true,
    data: {
      secrets: secretsWithMaskedValues,
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total,
        pages: Math.ceil(total / Number(limit)),
      },
    },
  });
}));

// Get secret by ID (with actual value for authorized users)
router.get('/:id', asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const { id } = req.params;
  const { reveal = false } = req.query;

  const secret = await prisma.secret.findFirst({
    where: {
      id,
      project: {
        members: {
          some: {
            userId: req.user!.id,
          },
        },
      },
    },
    include: {
      project: {
        select: {
          id: true,
          name: true,
          slug: true,
        },
      },
    },
  });

  if (!secret) {
    throw new CustomError('Secret not found', 404);
  }

  // Only reveal actual value if explicitly requested and user has permission
  let secretValue = secret.value;
  if (reveal === 'true') {
    try {
      secretValue = decryptSecret(secret.value);
    } catch (error) {
      logger.error('Failed to decrypt secret:', error);
      secretValue = secret.value; // Fallback to stored value
    }
  } else {
    secretValue = '*'.repeat(Math.min(secret.value.length, 20));
  }

  res.json({
    success: true,
    data: {
      secret: {
        ...secret,
        value: secretValue,
        maskedValue: reveal !== 'true',
      },
    },
  });
}));

// Create new secret
router.post('/', [
  body('key').isLength({ min: 1, max: 100 }).matches(/^[A-Z_][A-Z0-9_]*$/i).withMessage('Key must start with a letter or underscore and contain only letters, numbers, and underscores'),
  body('value').isLength({ min: 1, max: 10000 }),
  body('description').optional().isLength({ max: 500 }),
  body('projectId').isLength({ min: 20, max: 30 }).matches(/^c[a-z0-9]+$/),
], asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    const errorMessages = errors.array().map(error => `${error.type === 'field' ? error.path : 'unknown'}: ${error.msg}`).join(', ');
    throw new CustomError(`Validation failed: ${errorMessages}`, 400);
  }

  const { key, value, description, projectId } = req.body;

  // Check if user has access to project
  const projectMember = await prisma.projectMember.findFirst({
    where: {
      projectId,
      userId: req.user!.id,
      role: { in: ['OWNER', 'MAINTAINER', 'DEVELOPER'] },
    },
  });

  if (!projectMember) {
    throw new CustomError('Project not found or access denied', 404);
  }

  // Check if secret key already exists in project
  const existingSecret = await prisma.secret.findFirst({
    where: {
      projectId,
      key,
    },
  });

  if (existingSecret) {
    throw new CustomError('Secret with this key already exists in project', 409);
  }

  // Encrypt the secret value
  const encryptedValue = encryptSecret(value);

  // Store in database
  const secret = await prisma.secret.create({
    data: {
      key,
      value: encryptedValue,
      description,
      projectId,
    },
    include: {
      project: {
        select: {
          id: true,
          name: true,
          slug: true,
        },
      },
    },
  });

  // Also store in Vault if available
  const secretsManager = getSecretsManager();
  if (secretsManager) {
    try {
      const vaultPath = `secret/data/chainops/${projectId}/${key}`;
      await secretsManager.writeSecrets(vaultPath, { [key]: value });
      
      logger.info(`Secret ${key} stored in Vault`, {
        projectId,
        userId: req.user!.id,
        vaultPath,
      });
    } catch (error) {
      logger.error('Failed to store secret in Vault:', error);
      // Don't fail the request, database storage is sufficient
    }
  }

  res.status(201).json({
    success: true,
    message: 'Secret created successfully',
    data: {
      secret: {
        ...secret,
        value: '*'.repeat(Math.min(value.length, 20)),
        maskedValue: true,
      },
    },
  });
}));

// Update secret
router.put('/:id', [
  body('key').optional().isLength({ min: 1, max: 100 }).matches(/^[A-Z_][A-Z0-9_]*$/),
  body('value').optional().isLength({ min: 1, max: 10000 }),
  body('description').optional().isLength({ max: 500 }),
], asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new CustomError('Validation failed', 400);
  }

  const { id } = req.params;
  const { key, value, description } = req.body;

  // Check if user has permission to update secret
  const existingSecret = await prisma.secret.findFirst({
    where: {
      id,
      project: {
        members: {
          some: {
            userId: req.user!.id,
            role: { in: ['OWNER', 'MAINTAINER', 'DEVELOPER'] },
          },
        },
      },
    },
    include: {
      project: true,
    },
  });

  if (!existingSecret) {
    throw new CustomError('Secret not found or insufficient permissions', 404);
  }

  // If updating key, check for conflicts
  if (key && key !== existingSecret.key) {
    const conflictingSecret = await prisma.secret.findFirst({
      where: {
        projectId: existingSecret.projectId,
        key,
        id: { not: id },
      },
    });

    if (conflictingSecret) {
      throw new CustomError('Secret with this key already exists in project', 409);
    }
  }

  // Prepare update data
  const updateData: any = {};
  if (key) updateData.key = key;
  if (value) updateData.value = encryptSecret(value);
  if (description !== undefined) updateData.description = description;

  const secret = await prisma.secret.update({
    where: { id },
    data: updateData,
    include: {
      project: {
        select: {
          id: true,
          name: true,
          slug: true,
        },
      },
    },
  });

  // Update in Vault if available and value changed
  if (value) {
    const secretsManager = getSecretsManager();
    if (secretsManager) {
      try {
        const vaultPath = `secret/data/chainops/${existingSecret.projectId}/${key || existingSecret.key}`;
        await secretsManager.writeSecrets(vaultPath, { [key || existingSecret.key]: value });
        
        logger.info(`Secret ${key || existingSecret.key} updated in Vault`, {
          projectId: existingSecret.projectId,
          userId: req.user!.id,
          vaultPath,
        });
      } catch (error) {
        logger.error('Failed to update secret in Vault:', error);
      }
    }
  }

  res.json({
    success: true,
    message: 'Secret updated successfully',
    data: {
      secret: {
        ...secret,
        value: '*'.repeat(Math.min(value?.length || secret.value.length, 20)),
        maskedValue: true,
      },
    },
  });
}));

// Delete secret
router.delete('/:id', asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const { id } = req.params;

  // Check if user has permission to delete secret
  const secret = await prisma.secret.findFirst({
    where: {
      id,
      project: {
        members: {
          some: {
            userId: req.user!.id,
            role: { in: ['OWNER', 'MAINTAINER'] },
          },
        },
      },
    },
    include: {
      project: true,
    },
  });

  if (!secret) {
    throw new CustomError('Secret not found or insufficient permissions', 404);
  }

  // Delete from database
  await prisma.secret.delete({
    where: { id },
  });

  // Delete from Vault if available
  const secretsManager = getSecretsManager();
  if (secretsManager) {
    try {
      const vaultPath = `secret/data/chainops/${secret.projectId}/${secret.key}`;
      await secretsManager.deleteSecrets(vaultPath);

      logger.info(`Secret ${secret.key} deleted from Vault`, {
        projectId: secret.projectId,
        userId: req.user!.id,
        vaultPath,
      });
    } catch (error) {
      logger.error('Failed to delete secret from Vault:', error);
      // Don't fail the request, database deletion is sufficient
    }
  }

  res.json({
    success: true,
    message: 'Secret deleted successfully',
  });
}));

// Get secrets for pipeline execution (internal API)
router.get('/project/:projectId/pipeline/:pipelineId', asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const { projectId, pipelineId } = req.params;
  const { environment } = req.query;

  // Verify user has access to project
  const projectMember = await prisma.projectMember.findFirst({
    where: {
      projectId,
      userId: req.user!.id,
    },
  });

  if (!projectMember) {
    throw new CustomError('Project not found or access denied', 404);
  }

  // Get all secrets for the project
  const secrets = await prisma.secret.findMany({
    where: {
      projectId,
    },
    select: {
      key: true,
      value: true,
    },
  });

  // Decrypt secrets for pipeline execution
  const decryptedSecrets: Record<string, string> = {};
  for (const secret of secrets) {
    try {
      decryptedSecrets[secret.key] = decryptSecret(secret.value);
    } catch (error) {
      logger.error(`Failed to decrypt secret ${secret.key}:`, error);
      // Skip this secret rather than failing the entire request
    }
  }

  // Also fetch from Vault if available
  const secretsManager = getSecretsManager();
  if (secretsManager) {
    try {
      const vaultPath = `secret/data/chainops/${projectId}`;
      const vaultSecrets = await secretsManager.fetchSecrets(vaultPath);

      // Merge Vault secrets (Vault takes precedence)
      Object.assign(decryptedSecrets, vaultSecrets);

      logger.info(`Fetched secrets from Vault for pipeline execution`, {
        projectId,
        pipelineId,
        secretCount: Object.keys(vaultSecrets).length,
      });
    } catch (error) {
      logger.warn('Failed to fetch secrets from Vault, using database only:', error);
    }
  }

  res.json({
    success: true,
    data: {
      secrets: decryptedSecrets,
      environment: environment || 'default',
    },
  });
}));

export default router;
