import { Router, Response } from 'express';
import { body, validationResult } from 'express-validator';
import { prisma } from '../services/database';
import { asyncHandler, CustomError } from '../middleware/errorHandler';
import { AuthenticatedRequest } from '../middleware/auth';
import { QueueService } from '../services/queue';
import { logger } from '../utils/logger';
import yaml from 'yaml';

const router = Router();

// Get all pipelines for user's projects
router.get('/', asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const { page = 1, limit = 10, search, projectId, status } = req.query;
  
  const skip = (Number(page) - 1) * Number(limit);
  const take = Number(limit);

  const where: any = {
    project: {
      members: {
        some: {
          userId: req.user!.id,
        },
      },
    },
  };
  
  if (search) {
    where.OR = [
      { name: { contains: search as string, mode: 'insensitive' } },
      { description: { contains: search as string, mode: 'insensitive' } },
    ];
  }
  
  if (projectId) {
    where.projectId = projectId as string;
  }
  
  if (status) {
    where.status = status as string;
  }

  const [pipelines, total] = await Promise.all([
    prisma.pipeline.findMany({
      where,
      skip,
      take,
      include: {
        project: {
          select: {
            id: true,
            name: true,
            slug: true,
          },
        },
        user: {
          select: {
            id: true,
            username: true,
            firstName: true,
            lastName: true,
            avatar: true,
          },
        },
        runs: {
          take: 1,
          orderBy: { createdAt: 'desc' },
          select: {
            id: true,
            number: true,
            status: true,
            startedAt: true,
            finishedAt: true,
            createdAt: true,
          },
        },
        _count: {
          select: {
            runs: true,
          },
        },
      },
      orderBy: { updatedAt: 'desc' },
    }),
    prisma.pipeline.count({ where }),
  ]);

  res.json({
    success: true,
    data: {
      pipelines,
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total,
        pages: Math.ceil(total / Number(limit)),
      },
    },
  });
}));

// Validate pipeline configuration
const validatePipelineConfig = (config: any): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];

  try {
    // If config is a string, try to parse as YAML
    let parsedConfig = config;
    if (typeof config === 'string') {
      parsedConfig = yaml.parse(config);
    }

    // Basic validation
    if (!parsedConfig || typeof parsedConfig !== 'object') {
      errors.push('Configuration must be a valid object or YAML string');
      return { isValid: false, errors };
    }

    // Check for required fields
    if (!parsedConfig.jobs && !parsedConfig.steps) {
      errors.push('Configuration must contain either "jobs" or "steps"');
    }

    // Validate jobs structure if present
    if (parsedConfig.jobs) {
      if (typeof parsedConfig.jobs !== 'object') {
        errors.push('Jobs must be an object');
      } else {
        for (const [jobName, jobConfig] of Object.entries(parsedConfig.jobs)) {
          if (!jobConfig || typeof jobConfig !== 'object') {
            errors.push(`Job "${jobName}" must be an object`);
          }
        }
      }
    }

    return { isValid: errors.length === 0, errors };
  } catch (error) {
    errors.push(`Invalid YAML/JSON configuration: ${error instanceof Error ? error.message : 'Unknown error'}`);
    return { isValid: false, errors };
  }
};

// Create new pipeline
router.post('/', [
  body('name').isLength({ min: 1, max: 100 }),
  body('slug').isLength({ min: 1, max: 50 }).matches(/^[a-z0-9-]+$/),
  body('description').optional().isLength({ max: 500 }),
  body('projectId').isUUID(),
  body('config').custom((value) => {
    const validation = validatePipelineConfig(value);
    if (!validation.isValid) {
      throw new Error(validation.errors.join(', '));
    }
    return true;
  }),
], asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new CustomError('Validation failed', 400);
  }

  const { name, slug, description, projectId, config } = req.body;

  // Check if user has access to project
  const projectMember = await prisma.projectMember.findFirst({
    where: {
      projectId,
      userId: req.user!.id,
    },
  });

  if (!projectMember) {
    throw new CustomError('Project not found or access denied', 404);
  }

  // Check if pipeline slug already exists in project
  const existingPipeline = await prisma.pipeline.findFirst({
    where: {
      slug,
      projectId,
    },
  });

  if (existingPipeline) {
    throw new CustomError('Pipeline with this slug already exists in project', 409);
  }

  // Parse and normalize config
  let normalizedConfig = config;
  if (typeof config === 'string') {
    normalizedConfig = yaml.parse(config);
  }

  const pipeline = await prisma.pipeline.create({
    data: {
      name,
      slug,
      description,
      projectId,
      userId: req.user!.id,
      config: normalizedConfig,
    },
    include: {
      project: {
        select: {
          id: true,
          name: true,
          slug: true,
        },
      },
      user: {
        select: {
          id: true,
          username: true,
          firstName: true,
          lastName: true,
          avatar: true,
        },
      },
    },
  });

  // Register pipeline with pipeline engine
  try {
    await QueueService.addNotification({
      type: 'pipeline-created',
      pipelineId: pipeline.id,
      projectId,
      userId: req.user!.id,
      data: {
        name: pipeline.name,
        slug: pipeline.slug,
        config: normalizedConfig,
      },
    });

    logger.info(`Pipeline ${pipeline.name} registered with pipeline engine`, {
      pipelineId: pipeline.id,
      projectId,
      userId: req.user!.id,
    });
  } catch (error) {
    logger.error('Failed to register pipeline with pipeline engine:', error);
  }

  res.status(201).json({
    success: true,
    message: 'Pipeline created successfully',
    data: { pipeline },
  });
}));

// Get pipeline by ID
router.get('/:id', asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const { id } = req.params;

  const pipeline = await prisma.pipeline.findFirst({
    where: {
      id,
      project: {
        members: {
          some: {
            userId: req.user!.id,
          },
        },
      },
    },
    include: {
      project: {
        select: {
          id: true,
          name: true,
          slug: true,
        },
      },
      user: {
        select: {
          id: true,
          username: true,
          firstName: true,
          lastName: true,
          avatar: true,
        },
      },
      triggers: true,
      runs: {
        take: 10,
        orderBy: { createdAt: 'desc' },
        include: {
          jobs: {
            select: {
              id: true,
              name: true,
              status: true,
              startedAt: true,
              finishedAt: true,
            },
          },
        },
      },
      _count: {
        select: {
          runs: true,
        },
      },
    },
  });

  if (!pipeline) {
    throw new CustomError('Pipeline not found', 404);
  }

  res.json({
    success: true,
    data: { pipeline },
  });
}));

// Update pipeline
router.put('/:id', [
  body('name').optional().isLength({ min: 1, max: 100 }),
  body('description').optional().isLength({ max: 500 }),
  body('config').optional().isObject(),
  body('isActive').optional().isBoolean(),
], asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new CustomError('Validation failed', 400);
  }

  const { id } = req.params;
  const { name, description, config, isActive } = req.body;

  // Check if user has permission to update pipeline
  const pipeline = await prisma.pipeline.findFirst({
    where: {
      id,
      project: {
        members: {
          some: {
            userId: req.user!.id,
            role: { in: ['OWNER', 'MAINTAINER', 'DEVELOPER'] },
          },
        },
      },
    },
  });

  if (!pipeline) {
    throw new CustomError('Pipeline not found or insufficient permissions', 404);
  }

  const updatedPipeline = await prisma.pipeline.update({
    where: { id },
    data: {
      name,
      description,
      config,
      isActive,
    },
    include: {
      project: {
        select: {
          id: true,
          name: true,
          slug: true,
        },
      },
      user: {
        select: {
          id: true,
          username: true,
          firstName: true,
          lastName: true,
          avatar: true,
        },
      },
    },
  });

  res.json({
    success: true,
    message: 'Pipeline updated successfully',
    data: { pipeline: updatedPipeline },
  });
}));

// Trigger pipeline run
router.post('/:id/run', [
  body('variables').optional().isObject(),
  body('branch').optional().isString(),
], asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new CustomError('Validation failed', 400);
  }

  const { id } = req.params;
  const { variables, branch } = req.body;

  // Check if user has permission to run pipeline
  const pipeline = await prisma.pipeline.findFirst({
    where: {
      id,
      isActive: true,
      project: {
        members: {
          some: {
            userId: req.user!.id,
          },
        },
      },
    },
    include: {
      project: true,
    },
  });

  if (!pipeline) {
    throw new CustomError('Pipeline not found or inactive', 404);
  }

  // Get next run number
  const lastRun = await prisma.pipelineRun.findFirst({
    where: { pipelineId: id },
    orderBy: { number: 'desc' },
    select: { number: true },
  });

  const runNumber = (lastRun?.number || 0) + 1;

  // Create pipeline run
  const pipelineRun = await prisma.pipelineRun.create({
    data: {
      number: runNumber,
      pipelineId: id,
      status: 'PENDING',
      trigger: {
        type: 'MANUAL',
        userId: req.user!.id,
        branch: branch || pipeline.project.defaultBranch,
      },
      variables: variables || {},
    },
    include: {
      pipeline: {
        select: {
          id: true,
          name: true,
          config: true,
        },
      },
    },
  });

  // Fetch project secrets for pipeline execution
  let secrets: Record<string, string> = {};
  try {
    const secretsResponse = await fetch(`${process.env.API_BASE_URL || 'http://localhost:8081'}/api/secrets/project/${pipeline.projectId}/pipeline/${id}`, {
      headers: {
        'Authorization': `Bearer ${req.headers.authorization?.replace('Bearer ', '')}`,
      },
    });

    if (secretsResponse.ok) {
      const secretsData = await secretsResponse.json();
      secrets = secretsData.data?.secrets || {};
    }
  } catch (error) {
    logger.warn('Failed to fetch secrets for pipeline execution:', error);
  }

  // Add to queue for processing with enhanced payload
  await QueueService.addPipelineJob({
    id: pipelineRun.id,
    type: 'pipeline-run',
    payload: {
      pipelineRunId: pipelineRun.id,
      pipelineId: id,
      projectId: pipeline.projectId,
      config: pipeline.config,
      variables: variables || {},
      secrets,
      branch: branch || pipeline.project.defaultBranch,
      repositoryUrl: pipeline.project.repositoryUrl,
      trigger: {
        type: 'manual',
        userId: req.user!.id,
        timestamp: new Date().toISOString(),
      },
    },
    userId: req.user!.id,
    projectId: pipeline.projectId,
    pipelineId: id,
    runId: pipelineRun.id,
  }, {
    priority: 1, // High priority for manual triggers
    delay: 0,
  });

  // Update pipeline status to running
  await prisma.pipeline.update({
    where: { id },
    data: { status: 'RUNNING' },
  });

  logger.info(`Pipeline run triggered`, {
    pipelineRunId: pipelineRun.id,
    pipelineId: id,
    projectId: pipeline.projectId,
    userId: req.user!.id,
    branch: branch || pipeline.project.defaultBranch,
  });

  res.status(201).json({
    success: true,
    message: 'Pipeline run triggered successfully',
    data: {
      pipelineRun: {
        ...pipelineRun,
        pipeline: {
          id: pipeline.id,
          name: pipeline.name,
          project: {
            id: pipeline.project.id,
            name: pipeline.project.name,
          },
        },
      },
    },
  });
}));

// Delete pipeline
router.delete('/:id', asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const { id } = req.params;

  // Check if user has permission to delete pipeline
  const pipeline = await prisma.pipeline.findFirst({
    where: {
      id,
      project: {
        members: {
          some: {
            userId: req.user!.id,
            role: { in: ['OWNER', 'MAINTAINER'] },
          },
        },
      },
    },
  });

  if (!pipeline) {
    throw new CustomError('Pipeline not found or insufficient permissions', 404);
  }

  await prisma.pipeline.delete({
    where: { id },
  });

  res.json({
    success: true,
    message: 'Pipeline deleted successfully',
  });
}));

// Get pipeline runs
router.get('/:id/runs', asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const { id } = req.params;
  const { page = 1, limit = 20 } = req.query;

  const skip = (Number(page) - 1) * Number(limit);
  const take = Number(limit);

  // Check if user has access to pipeline
  const pipeline = await prisma.pipeline.findFirst({
    where: {
      id,
      project: {
        members: {
          some: {
            userId: req.user!.id,
          },
        },
      },
    },
  });

  if (!pipeline) {
    throw new CustomError('Pipeline not found', 404);
  }

  const [runs, total] = await Promise.all([
    prisma.pipelineRun.findMany({
      where: { pipelineId: id },
      skip,
      take,
      include: {
        jobs: {
          select: {
            id: true,
            name: true,
            status: true,
            startedAt: true,
            finishedAt: true,
          },
        },
      },
      orderBy: { createdAt: 'desc' },
    }),
    prisma.pipelineRun.count({
      where: { pipelineId: id },
    }),
  ]);

  res.json({
    success: true,
    data: {
      runs,
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total,
        pages: Math.ceil(total / Number(limit)),
      },
    },
  });
}));

// Get pipeline statistics
router.get('/:id/stats', asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const { id } = req.params;

  // Check if user has access to pipeline
  const pipeline = await prisma.pipeline.findFirst({
    where: {
      id,
      project: {
        members: {
          some: {
            userId: req.user!.id,
          },
        },
      },
    },
  });

  if (!pipeline) {
    throw new CustomError('Pipeline not found', 404);
  }

  // Get pipeline statistics
  const [totalRuns, successfulRuns, failedRuns, avgDuration] = await Promise.all([
    prisma.pipelineRun.count({
      where: { pipelineId: id },
    }),
    prisma.pipelineRun.count({
      where: { pipelineId: id, status: 'SUCCESS' },
    }),
    prisma.pipelineRun.count({
      where: { pipelineId: id, status: 'FAILED' },
    }),
    prisma.pipelineRun.aggregate({
      where: {
        pipelineId: id,
        status: 'SUCCESS',
        startedAt: { not: null },
        finishedAt: { not: null },
      },
      _avg: {
        number: true,
      },
    }),
  ]);

  const successRate = totalRuns > 0 ? Math.round((successfulRuns / totalRuns) * 100) : 0;

  res.json({
    success: true,
    data: {
      totalRuns,
      successfulRuns,
      failedRuns,
      successRate,
      avgDuration: avgDuration._avg?.number || 0,
    },
  });
}));

export default router;
