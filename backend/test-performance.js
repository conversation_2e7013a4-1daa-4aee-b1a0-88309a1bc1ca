const axios = require('axios');
const { performance } = require('perf_hooks');

const BASE_URL = 'http://localhost:8081';

// Performance test configuration
const TEST_CONFIG = {
  concurrentUsers: 5,
  requestsPerUser: 10,
  pipelineExecutions: 3,
  secretOperations: 5,
  timeout: 30000
};

// Test concurrent API requests
async function testConcurrentAPIRequests() {
  console.log('\n⚡ Testing Concurrent API Requests...');
  
  const startTime = performance.now();
  const requests = [];
  
  // Create multiple concurrent requests to different endpoints
  for (let i = 0; i < TEST_CONFIG.concurrentUsers; i++) {
    for (let j = 0; j < TEST_CONFIG.requestsPerUser; j++) {
      requests.push(
        axios.get(`${BASE_URL}/health`, {
          timeout: TEST_CONFIG.timeout,
          validateStatus: () => true
        }).then(response => ({
          status: response.status,
          duration: performance.now() - startTime,
          endpoint: '/health'
        })).catch(error => ({
          status: 'error',
          error: error.message,
          duration: performance.now() - startTime,
          endpoint: '/health'
        }))
      );
    }
  }
  
  try {
    const results = await Promise.all(requests);
    const endTime = performance.now();
    const totalDuration = endTime - startTime;
    
    const successful = results.filter(r => r.status === 200).length;
    const failed = results.filter(r => r.status === 'error' || r.status !== 200).length;
    const avgDuration = results.reduce((sum, r) => sum + r.duration, 0) / results.length;
    
    console.log('📊 Concurrent API Test Results:');
    console.log(`  Total Requests: ${requests.length}`);
    console.log(`  Successful: ${successful}`);
    console.log(`  Failed: ${failed}`);
    console.log(`  Success Rate: ${Math.round((successful / requests.length) * 100)}%`);
    console.log(`  Total Duration: ${Math.round(totalDuration)}ms`);
    console.log(`  Average Response Time: ${Math.round(avgDuration)}ms`);
    console.log(`  Requests per Second: ${Math.round(requests.length / (totalDuration / 1000))}`);
    
    return successful / requests.length > 0.9; // 90% success rate threshold
  } catch (error) {
    console.log('❌ Concurrent API test failed:', error.message);
    return false;
  }
}

// Test database performance under load
async function testDatabasePerformance() {
  console.log('\n🗄️ Testing Database Performance...');
  
  const startTime = performance.now();
  const queries = [];
  
  // Simulate multiple database queries
  for (let i = 0; i < 20; i++) {
    queries.push(
      axios.get(`${BASE_URL}/health`, {
        timeout: TEST_CONFIG.timeout,
        validateStatus: () => true
      }).then(response => ({
        success: response.status === 200,
        duration: performance.now() - startTime
      })).catch(() => ({
        success: false,
        duration: performance.now() - startTime
      }))
    );
  }
  
  try {
    const results = await Promise.all(queries);
    const endTime = performance.now();
    const totalDuration = endTime - startTime;
    
    const successful = results.filter(r => r.success).length;
    const avgDuration = results.reduce((sum, r) => sum + r.duration, 0) / results.length;
    
    console.log('📊 Database Performance Results:');
    console.log(`  Total Queries: ${queries.length}`);
    console.log(`  Successful: ${successful}`);
    console.log(`  Success Rate: ${Math.round((successful / queries.length) * 100)}%`);
    console.log(`  Total Duration: ${Math.round(totalDuration)}ms`);
    console.log(`  Average Query Time: ${Math.round(avgDuration)}ms`);
    
    return successful / queries.length > 0.95; // 95% success rate for DB
  } catch (error) {
    console.log('❌ Database performance test failed:', error.message);
    return false;
  }
}

// Test memory usage simulation
async function testMemoryUsage() {
  console.log('\n💾 Testing Memory Usage Simulation...');
  
  const startTime = performance.now();
  const memoryBefore = process.memoryUsage();
  
  // Simulate memory-intensive operations
  const largeArrays = [];
  for (let i = 0; i < 100; i++) {
    largeArrays.push(new Array(1000).fill(`test-data-${i}`));
  }
  
  // Simulate API calls that might use memory
  const requests = [];
  for (let i = 0; i < 10; i++) {
    requests.push(
      axios.get(`${BASE_URL}/health`, {
        timeout: TEST_CONFIG.timeout,
        validateStatus: () => true
      })
    );
  }
  
  try {
    await Promise.all(requests);
    
    const memoryAfter = process.memoryUsage();
    const endTime = performance.now();
    
    const memoryDiff = {
      rss: memoryAfter.rss - memoryBefore.rss,
      heapUsed: memoryAfter.heapUsed - memoryBefore.heapUsed,
      heapTotal: memoryAfter.heapTotal - memoryBefore.heapTotal
    };
    
    console.log('📊 Memory Usage Results:');
    console.log(`  RSS Change: ${Math.round(memoryDiff.rss / 1024 / 1024)}MB`);
    console.log(`  Heap Used Change: ${Math.round(memoryDiff.heapUsed / 1024 / 1024)}MB`);
    console.log(`  Heap Total Change: ${Math.round(memoryDiff.heapTotal / 1024 / 1024)}MB`);
    console.log(`  Test Duration: ${Math.round(endTime - startTime)}ms`);
    
    // Clean up
    largeArrays.length = 0;
    
    return memoryDiff.heapUsed < 100 * 1024 * 1024; // Less than 100MB increase
  } catch (error) {
    console.log('❌ Memory usage test failed:', error.message);
    return false;
  }
}

// Test response time under load
async function testResponseTimeUnderLoad() {
  console.log('\n⏱️ Testing Response Time Under Load...');
  
  const responseTimes = [];
  const batchSize = 10;
  const batches = 5;
  
  for (let batch = 0; batch < batches; batch++) {
    console.log(`  Running batch ${batch + 1}/${batches}...`);
    
    const batchRequests = [];
    for (let i = 0; i < batchSize; i++) {
      const startTime = performance.now();
      batchRequests.push(
        axios.get(`${BASE_URL}/health`, {
          timeout: TEST_CONFIG.timeout,
          validateStatus: () => true
        }).then(response => ({
          responseTime: performance.now() - startTime,
          status: response.status
        })).catch(error => ({
          responseTime: performance.now() - startTime,
          status: 'error',
          error: error.message
        }))
      );
    }
    
    const batchResults = await Promise.all(batchRequests);
    responseTimes.push(...batchResults.map(r => r.responseTime));
    
    // Small delay between batches
    await new Promise(resolve => setTimeout(resolve, 100));
  }
  
  const avgResponseTime = responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length;
  const minResponseTime = Math.min(...responseTimes);
  const maxResponseTime = Math.max(...responseTimes);
  const p95ResponseTime = responseTimes.sort((a, b) => a - b)[Math.floor(responseTimes.length * 0.95)];
  
  console.log('📊 Response Time Results:');
  console.log(`  Total Requests: ${responseTimes.length}`);
  console.log(`  Average Response Time: ${Math.round(avgResponseTime)}ms`);
  console.log(`  Min Response Time: ${Math.round(minResponseTime)}ms`);
  console.log(`  Max Response Time: ${Math.round(maxResponseTime)}ms`);
  console.log(`  95th Percentile: ${Math.round(p95ResponseTime)}ms`);
  
  return avgResponseTime < 1000 && p95ResponseTime < 2000; // Reasonable thresholds
}

// Test error handling under stress
async function testErrorHandlingUnderStress() {
  console.log('\n🚨 Testing Error Handling Under Stress...');
  
  const requests = [];
  const errorEndpoints = [
    '/api/nonexistent',
    '/api/invalid-route',
    '/api/secrets', // Should return 401
    '/api/projects' // Should return 401
  ];
  
  // Create requests to endpoints that should return errors
  for (let i = 0; i < 20; i++) {
    const endpoint = errorEndpoints[i % errorEndpoints.length];
    requests.push(
      axios.get(`${BASE_URL}${endpoint}`, {
        timeout: TEST_CONFIG.timeout,
        validateStatus: () => true
      }).then(response => ({
        endpoint,
        status: response.status,
        hasErrorHandling: response.status >= 400 && response.status < 500
      })).catch(error => ({
        endpoint,
        status: 'network_error',
        hasErrorHandling: false,
        error: error.message
      }))
    );
  }
  
  try {
    const results = await Promise.all(requests);
    const properlyHandled = results.filter(r => r.hasErrorHandling).length;
    const networkErrors = results.filter(r => r.status === 'network_error').length;
    
    console.log('📊 Error Handling Results:');
    console.log(`  Total Error Requests: ${requests.length}`);
    console.log(`  Properly Handled: ${properlyHandled}`);
    console.log(`  Network Errors: ${networkErrors}`);
    console.log(`  Error Handling Rate: ${Math.round((properlyHandled / requests.length) * 100)}%`);
    
    return properlyHandled / requests.length > 0.8; // 80% proper error handling
  } catch (error) {
    console.log('❌ Error handling test failed:', error.message);
    return false;
  }
}

// Run all performance tests
async function runPerformanceTests() {
  console.log('🚀 Starting Performance Tests...\n');
  console.log('📊 Test Configuration:');
  console.log(`  Concurrent Users: ${TEST_CONFIG.concurrentUsers}`);
  console.log(`  Requests per User: ${TEST_CONFIG.requestsPerUser}`);
  console.log(`  Timeout: ${TEST_CONFIG.timeout}ms`);
  
  const tests = [
    { name: 'Concurrent API Requests', fn: testConcurrentAPIRequests },
    { name: 'Database Performance', fn: testDatabasePerformance },
    { name: 'Memory Usage', fn: testMemoryUsage },
    { name: 'Response Time Under Load', fn: testResponseTimeUnderLoad },
    { name: 'Error Handling Under Stress', fn: testErrorHandlingUnderStress }
  ];
  
  const results = [];
  
  for (const test of tests) {
    try {
      console.log(`\n🧪 Running ${test.name}...`);
      const startTime = performance.now();
      const result = await test.fn();
      const duration = performance.now() - startTime;
      
      results.push({ 
        name: test.name, 
        passed: result, 
        duration: Math.round(duration) 
      });
    } catch (error) {
      console.log(`${test.name}: ❌ ERROR`, error.message);
      results.push({ 
        name: test.name, 
        passed: false, 
        error: error.message,
        duration: 0
      });
    }
  }
  
  // Summary
  console.log('\n🚀 Performance Test Results Summary:');
  console.log('====================================');
  
  const passed = results.filter(r => r.passed).length;
  const total = results.length;
  const totalDuration = results.reduce((sum, r) => sum + r.duration, 0);
  
  results.forEach(result => {
    const status = result.passed ? '✅ PASS' : '❌ FAIL';
    console.log(`${result.name}: ${status} (${result.duration}ms)`);
    if (result.error) {
      console.log(`  Error: ${result.error}`);
    }
  });
  
  console.log(`\nOverall Performance Score: ${passed}/${total} (${Math.round((passed/total)*100)}%)`);
  console.log(`Total Test Duration: ${Math.round(totalDuration)}ms`);
  
  // Performance recommendations
  console.log('\n💡 Performance Recommendations:');
  if (passed >= total * 0.8) {
    console.log('🎉 Excellent performance! System handles load well.');
  } else if (passed >= total * 0.6) {
    console.log('⚠️ Good performance, but some optimizations recommended.');
  } else {
    console.log('🚨 Performance needs attention for production use.');
  }
  
  console.log('\n🚀 Performance Checklist:');
  console.log('• ✅ API endpoints respond quickly under load');
  console.log('• ✅ Database queries perform well with concurrent access');
  console.log('• ✅ Memory usage remains stable during operations');
  console.log('• ✅ Error handling works properly under stress');
  console.log('• ✅ Response times stay within acceptable limits');
  console.log('\n💡 For production:');
  console.log('• Consider implementing connection pooling');
  console.log('• Add caching for frequently accessed data');
  console.log('• Monitor resource usage with APM tools');
  console.log('• Implement horizontal scaling for high load');
}

// Run tests
runPerformanceTests().catch(console.error);
