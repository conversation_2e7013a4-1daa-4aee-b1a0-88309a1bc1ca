const axios = require('axios');

const BASE_URL = 'http://localhost:8081';

// Use existing user credentials
const authToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOiJjbWJrdnl6cGcwMDBqMTRwa2FwZTFhNGNjIiwiaWF0IjoxNzMzNDk0NzI3LCJleHAiOjE3MzQwOTk1Mjd9.Ey8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8'; // Replace with actual token
const projectId = 'cmbkw262w000i14pkc7us66bl'; // Replace with actual project ID

async function testPipelineValidation() {
  console.log('Testing pipeline validation...');
  
  const pipelineData = {
    name: 'Debug Pipeline',
    slug: 'debug-pipeline',
    description: 'Debug pipeline',
    projectId: projectId,
    config: {
      name: 'Debug CI/CD',
      on: { push: { branches: ['main'] } },
      jobs: {
        build: {
          'runs-on': 'ubuntu-latest',
          steps: [
            { name: 'Checkout', uses: 'actions/checkout@v3' }
          ]
        }
      }
    }
  };
  
  try {
    const response = await axios.post(`${BASE_URL}/api/pipelines`, pipelineData, {
      headers: {
        'Authorization': `Bearer ${authToken}`,
        'Content-Type': 'application/json'
      }
    });
    console.log('Pipeline creation success:', response.data);
  } catch (error) {
    console.log('Pipeline creation error:', error.response?.data || error.message);
    console.log('Status:', error.response?.status);
  }
}

async function testSecretsValidation() {
  console.log('\nTesting secrets validation...');
  
  const secretData = {
    key: 'DEBUG_API_KEY',
    value: 'debug-secret-value',
    description: 'Debug API key',
    projectId: projectId
  };
  
  try {
    const response = await axios.post(`${BASE_URL}/api/secrets`, secretData, {
      headers: {
        'Authorization': `Bearer ${authToken}`,
        'Content-Type': 'application/json'
      }
    });
    console.log('Secret creation success:', response.data);
  } catch (error) {
    console.log('Secret creation error:', error.response?.data || error.message);
    console.log('Status:', error.response?.status);
  }
}

async function runDebugTests() {
  await testPipelineValidation();
  await testSecretsValidation();
}

runDebugTests().catch(console.error);
