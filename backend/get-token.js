const axios = require('axios');

const BASE_URL = 'http://localhost:8081';

async function getAuthToken() {
  try {
    const response = await axios.post(`${BASE_URL}/auth/login`, {
      login: '<EMAIL>',
      password: 'testpassword123'
    });
    
    console.log('Auth token:', response.data.data.token);
    console.log('User ID:', response.data.data.user.id);
    return response.data.data.token;
  } catch (error) {
    console.log('Login error:', error.response?.data || error.message);
    return null;
  }
}

getAuthToken();
